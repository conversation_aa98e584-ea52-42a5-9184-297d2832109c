# 鼠标坐标捕捉工具

## 📋 功能说明
- 使用 `Alt + C` 快捷键捕捉当前鼠标坐标
- 自动将坐标复制到剪贴板
- 显示历史记录
- 支持开机启动
- 系统托盘运行

## 🚀 使用方法
1. 双击 `鼠标坐标捕捉工具.exe` 启动程序
2. 程序会最小化到系统托盘
3. 勾选"启用监听"开始监听快捷键
4. 按 `Alt + C` 捕捉鼠标坐标
5. 坐标会自动复制到剪贴板

## ⚙️ 设置选项
- **启用监听**: 开启/关闭快捷键监听
- **开机启动**: 设置程序开机自动启动

## 📁 文件说明
- `鼠标坐标捕捉工具.exe`: 主程序文件
- `rabbear16x16.ico`: 程序图标
- `用法.txt`: 详细使用说明
- `README.md`: 本说明文件

## 🔧 修复说明
本版本修复了以下问题：
- 移除了对 `winshell` 和 `win32com` 的依赖，避免DLL加载错误
- 使用批处理文件方式实现开机启动功能
- 优化了打包配置，减少了依赖冲突

## 💡 注意事项
- 程序需要管理员权限来设置开机启动
- 如果开机启动设置失败，可以手动将程序快捷方式复制到启动文件夹
- 启动文件夹位置：`%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup`

## 🐛 故障排除
如果程序无法启动，请检查：
1. 是否有杀毒软件阻止程序运行
2. 是否缺少必要的系统组件
3. 尝试以管理员身份运行

## 📞 技术支持
如有问题，请检查控制台输出或联系开发者。

---
版本: 1.1
更新日期: 2025-08-27

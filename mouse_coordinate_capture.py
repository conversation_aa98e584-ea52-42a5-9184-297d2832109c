import os
import sys
import json
import pyperclip
from pynput import keyboard, mouse
import tkinter as tk
from tkinter import messagebox
import pystray
from PIL import Image
import threading

# 历史记录文件路径
HISTORY_FILE = 'mouse_coordinate_history.json'

class MouseCoordinateManager:
    def __init__(self):
        # 初始化变量
        self.is_listening = False
        self.hotkey_listener = None
        self.history = []
        self.load_history()

        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("鼠标坐标管理器")
        self.root.withdraw()  # 初始时隐藏窗口

        # 开机启动勾选框
        self.startup_var = tk.IntVar()
        self.startup_checkbox = tk.Checkbutton(self.root, text="开机启动", variable=self.startup_var, command=self.toggle_startup)
        self.startup_checkbox.pack(pady=5)

        # 启用监听按钮
        self.listen_var = tk.IntVar()
        self.listen_checkbox = tk.Checkbutton(self.root, text="启用监听", variable=self.listen_var, command=self.toggle_listening)
        self.listen_checkbox.pack(pady=5)

        # 显示最后复制内容的标签
        self.last_copied_label = tk.Label(self.root, text="最后复制的坐标: ")
        self.last_copied_label.pack(pady=5)

        # 历史记录显示区域
        self.history_text = tk.Text(self.root, height=10, width=30)
        self.history_text.pack(pady=10)
        self.update_history_display()

        # 初始化开机启动状态
        self.check_startup()

        # 加载图标
        self.icon_image = Image.open('rabbear16x16.ico')  # 请准备一个名为 icon.png 的图标文件
        self.icon = pystray.Icon("name", self.icon_image, "鼠标坐标管理器", menu=pystray.Menu(
            pystray.MenuItem('显示窗口', self.show_window),
            pystray.MenuItem('退出', self.quit_app)
        ))

        # 启动系统托盘图标线程
        self.icon_thread = threading.Thread(target=self.icon.run)
        self.icon_thread.daemon = True
        self.icon_thread.start()

        # 绑定系统托盘图标双击事件
        self.icon.clicked = self.on_icon_double_click

    def load_history(self):
        try:
            with open(HISTORY_FILE, 'r') as f:
                self.history = json.load(f)
        except FileNotFoundError:
            self.history = []

    def save_history(self):
        with open(HISTORY_FILE, 'w') as f:
            json.dump(self.history, f)

    def update_history_display(self):
        self.history_text.delete(1.0, tk.END)
        for item in self.history:
            self.history_text.insert(tk.END, f"{item}\n")
        if self.history:
            self.last_copied_label.config(text=f"最后复制的坐标: {self.history[-1]}")
        else:
            self.last_copied_label.config(text="最后复制的坐标: 无")

    def on_activate(self):
        # 获取当前鼠标的坐标
        current_mouse = mouse.Controller()
        position = current_mouse.position
        # 将坐标复制到剪贴板
        pyperclip.copy(str(position))
        print(f"鼠标坐标 {position} 已复制到剪贴板")
        # 记录历史
        self.history.append(str(position))
        self.save_history()
        self.update_history_display()

    def toggle_listening(self):
        if self.listen_var.get():
            self.is_listening = True
            hotkey = keyboard.HotKey(
                keyboard.HotKey.parse('<alt>+c'),
                self.on_activate
            )

            def for_canonical(f):
                return lambda k: f(self.hotkey_listener.canonical(k))

            self.hotkey_listener = keyboard.Listener(
                on_press=for_canonical(hotkey.press),
                on_release=for_canonical(hotkey.release)
            )
            self.hotkey_listener.start()
        else:
            self.is_listening = False
            if self.hotkey_listener:
                self.hotkey_listener.stop()
                self.hotkey_listener = None

    def check_startup(self):
        startup_folder = os.path.join(os.environ['APPDATA'], 'Microsoft', 'Windows', 'Start Menu', 'Programs', 'Startup')
        shortcut_path = os.path.join(startup_folder, 'MouseCoordinateManager.lnk')
        bat_path = os.path.join(startup_folder, 'MouseCoordinateManager.bat')
        if os.path.exists(shortcut_path) or os.path.exists(bat_path):
            self.startup_var.set(1)

    def toggle_startup(self):
        startup_folder = os.path.join(os.environ['APPDATA'], 'Microsoft', 'Windows', 'Start Menu', 'Programs', 'Startup')
        shortcut_path = os.path.join(startup_folder, 'MouseCoordinateManager.lnk')
        if self.startup_var.get():
            # 创建快捷方式 - 使用批处理文件方法
            try:
                # 创建一个批处理文件来启动程序
                if getattr(sys, 'frozen', False):
                    # 如果是打包后的exe文件
                    target_path = sys.executable
                else:
                    # 如果是Python脚本
                    target_path = f'"{sys.executable}" "{os.path.abspath(__file__)}"'

                bat_content = f'@echo off\ncd /d "{os.path.dirname(os.path.abspath(__file__) if not getattr(sys, "frozen", False) else sys.executable)}"\nstart "" {target_path if getattr(sys, "frozen", False) else target_path}'
                bat_path = os.path.join(startup_folder, 'MouseCoordinateManager.bat')

                with open(bat_path, 'w', encoding='utf-8') as f:
                    f.write(bat_content)

                print("开机启动已设置（使用批处理文件）")
            except Exception as e:
                print(f"创建开机启动失败: {e}")
                messagebox.showwarning("警告", f"设置开机启动失败: {e}\n您可以手动将程序快捷方式复制到启动文件夹")
                self.startup_var.set(0)  # 重置复选框状态
        else:
            # 删除启动文件
            bat_path = os.path.join(startup_folder, 'MouseCoordinateManager.bat')
            lnk_path = shortcut_path

            for path in [bat_path, lnk_path]:
                if os.path.exists(path):
                    try:
                        os.remove(path)
                        print(f"已删除启动文件: {path}")
                    except Exception as e:
                        print(f"删除启动文件失败: {e}")

    def show_window(self):
        self.root.deiconify()

    def on_icon_double_click(self):
        self.show_window()

    def quit_app(self):
        self.icon.stop()
        if self.hotkey_listener:
            self.hotkey_listener.stop()
        self.root.quit()

    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    app = MouseCoordinateManager()
    app.run()
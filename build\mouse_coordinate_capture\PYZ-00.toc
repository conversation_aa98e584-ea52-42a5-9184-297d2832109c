('E:\\onedrive\\OneDrive\\Tools\\AI生成工具\\鼠标坐标捕捉工具\\build\\mouse_coordinate_capture\\PYZ-00.pyz',
 [('_distutils_hack',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\contextlib.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('configparser',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\configparser.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\zipfile.py',
   'PYMODULE'),
  ('argparse',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\argparse.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\textwrap.py',
   'PYMODULE'),
  ('copy', 'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\copy.py', 'PYMODULE'),
  ('gettext',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\gettext.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\py_compile.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('lzma', 'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\lzma.py', 'PYMODULE'),
  ('_compression',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\_compression.py',
   'PYMODULE'),
  ('bz2', 'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\bz2.py', 'PYMODULE'),
  ('_strptime',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\_strptime.py',
   'PYMODULE'),
  ('datetime',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\datetime.py',
   'PYMODULE'),
  ('calendar',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\calendar.py',
   'PYMODULE'),
  ('struct',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\struct.py',
   'PYMODULE'),
  ('shutil',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\shutil.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\tarfile.py',
   'PYMODULE'),
  ('gzip', 'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\gzip.py', 'PYMODULE'),
  ('email',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('socket',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\socket.py',
   'PYMODULE'),
  ('selectors',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\selectors.py',
   'PYMODULE'),
  ('random',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\random.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\hashlib.py',
   'PYMODULE'),
  ('logging',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\pickle.py',
   'PYMODULE'),
  ('pprint',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\pprint.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('string',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\string.py',
   'PYMODULE'),
  ('bisect',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\bisect.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.message',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\base64.py',
   'PYMODULE'),
  ('getopt',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\getopt.py',
   'PYMODULE'),
  ('quopri',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\quopri.py',
   'PYMODULE'),
  ('uu', 'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\uu.py', 'PYMODULE'),
  ('optparse',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\optparse.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.header',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\email\\errors.py',
   'PYMODULE'),
  ('csv', 'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\csv.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('setuptools',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\_vendor\\pyparsing.py',
   'PYMODULE'),
  ('uuid', 'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\uuid.py', 'PYMODULE'),
  ('ctypes.util',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\tempfile.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('netbios',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\win32\\lib\\netbios.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\subprocess.py',
   'PYMODULE'),
  ('signal',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\signal.py',
   'PYMODULE'),
  ('platform',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\platform.py',
   'PYMODULE'),
  ('pdb', 'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\pdb.py', 'PYMODULE'),
  ('pydoc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\pydoc.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\webbrowser.py',
   'PYMODULE'),
  ('http.server',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\http\\server.py',
   'PYMODULE'),
  ('http',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\socketserver.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\mimetypes.py',
   'PYMODULE'),
  ('http.client',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\http\\client.py',
   'PYMODULE'),
  ('ssl', 'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\ssl.py', 'PYMODULE'),
  ('html',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\html\\entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty', 'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\tty.py', 'PYMODULE'),
  ('sysconfig',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\sysconfig.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\zipimport.py',
   'PYMODULE'),
  ('runpy',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\runpy.py',
   'PYMODULE'),
  ('shlex',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\shlex.py',
   'PYMODULE'),
  ('inspect',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\inspect.py',
   'PYMODULE'),
  ('ast', 'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\ast.py', 'PYMODULE'),
  ('glob', 'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\glob.py', 'PYMODULE'),
  ('code', 'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\code.py', 'PYMODULE'),
  ('codeop',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\codeop.py',
   'PYMODULE'),
  ('__future__',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\__future__.py',
   'PYMODULE'),
  ('dis', 'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\dis.py', 'PYMODULE'),
  ('opcode',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\opcode.py',
   'PYMODULE'),
  ('bdb', 'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\cmd.py', 'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('typing',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\typing.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('imp', 'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\imp.py', 'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._typing',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_typing.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._compat',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.__about__',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\_vendor\\ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\_vendor\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('distutils.log',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.file_util',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.spawn',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.debug',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('_osx_support',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\_osx_support.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils.command',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('queue',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\queue.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\secrets.py',
   'PYMODULE'),
  ('hmac', 'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\hmac.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('decimal',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\contextvars.py',
   'PYMODULE'),
  ('numbers',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\numbers.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('site', 'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site.py', 'PYMODULE'),
  ('rlcompleter',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\rlcompleter.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('distutils.extension',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\distutils\\extension.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('distutils.filelist',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.py34compat',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\py34compat.py',
   'PYMODULE'),
  ('distutils.version',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\distutils\\version.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.install_scripts',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools.command.easy_install',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\command\\easy_install.py',
   'PYMODULE'),
  ('setuptools.package_index',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\package_index.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.sandbox',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\sandbox.py',
   'PYMODULE'),
  ('distutils.command.build_scripts',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('distutils.command.install',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\distutils\\command\\install.py',
   'PYMODULE'),
  ('distutils.command.install_scripts',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.py36compat',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\command\\py36compat.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('pkg_resources',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging.utils',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.metadata',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.licenses',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\dataclasses.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._typing',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_typing.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._compat',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.__about__',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.appdirs',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pkg_resources\\_vendor\\appdirs.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('plistlib',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\plistlib.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\config.py',
   'PYMODULE'),
  ('setuptools.extern',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\extern\\__init__.py',
   'PYMODULE'),
  ('distutils.dist',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\distutils\\dist.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('distutils.cmd',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools.ssl_support',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\ssl_support.py',
   'PYMODULE'),
  ('wincertstore',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\wincertstore.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools._deprecation_warning',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\setuptools\\_deprecation_warning.py',
   'PYMODULE'),
  ('distutils.util',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.errors',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.core',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.config',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\distutils\\config.py',
   'PYMODULE'),
  ('cgi', 'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\cgi.py', 'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('PIL._tkinter_finder',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\_tkinter_finder.py',
   'PYMODULE'),
  ('pynput.mouse._win32',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pynput\\mouse\\_win32.py',
   'PYMODULE'),
  ('pynput.mouse._base',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pynput\\mouse\\_base.py',
   'PYMODULE'),
  ('pynput._util.win32',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pynput\\_util\\win32.py',
   'PYMODULE'),
  ('pynput._util.win32_vks',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pynput\\_util\\win32_vks.py',
   'PYMODULE'),
  ('pynput._util',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pynput\\_util\\__init__.py',
   'PYMODULE'),
  ('six',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\six.py',
   'PYMODULE'),
  ('pynput.keyboard._win32',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pynput\\keyboard\\_win32.py',
   'PYMODULE'),
  ('pynput.keyboard._base',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pynput\\keyboard\\_base.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\stringprep.py',
   'PYMODULE'),
  ('getpass',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\getpass.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\ftplib.py',
   'PYMODULE'),
  ('netrc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\netrc.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\_py_abc.py',
   'PYMODULE'),
  ('threading',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\_threading_local.py',
   'PYMODULE'),
  ('PIL.Image',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.features',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('colorsys',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\colorsys.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('fractions',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\fractions.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('numpy',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy._version',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\_version.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.nosetester',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\testing\\_private\\nosetester.py',
   'PYMODULE'),
  ('doctest',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\doctest.py',
   'PYMODULE'),
  ('difflib',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\difflib.py',
   'PYMODULE'),
  ('numpy.testing._private.noseclasses',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\testing\\_private\\noseclasses.py',
   'PYMODULE'),
  ('numpy.testing._private.decorators',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\testing\\_private\\decorators.py',
   'PYMODULE'),
  ('numpy.testing._private.parameterized',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\testing\\_private\\parameterized.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('psutil',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._psaix',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\psutil\\_psaix.py',
   'PYMODULE'),
  ('psutil._pssunos',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\psutil\\_pssunos.py',
   'PYMODULE'),
  ('psutil._psbsd',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\psutil\\_psbsd.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('psutil._psosx',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\psutil\\_psosx.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psutil._pslinux',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\psutil\\_pslinux.py',
   'PYMODULE'),
  ('psutil._psposix',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\psutil\\_psposix.py',
   'PYMODULE'),
  ('psutil._compat',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\psutil\\_compat.py',
   'PYMODULE'),
  ('dummy_threading',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\dummy_threading.py',
   'PYMODULE'),
  ('_dummy_thread',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\_dummy_thread.py',
   'PYMODULE'),
  ('psutil._common',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.compat._inspect',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\compat\\_inspect.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('unittest',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.core.records',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('threadpoolctl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\threadpoolctl.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.compat',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy.array_api',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._generic_alias',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\_typing\\_generic_alias.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('cffi',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi.error',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.api',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.verifier',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cffi.lock',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.cparser',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pycparser',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.ply',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.model',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('PIL._util',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._typing',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('typing_extensions',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._binary',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL._version',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('pystray',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pystray\\__init__.py',
   'PYMODULE'),
  ('pystray._util.win32',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pystray\\_util\\win32.py',
   'PYMODULE'),
  ('pystray._util.notify_dbus',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pystray\\_util\\notify_dbus.py',
   'PYMODULE'),
  ('pystray._util.gtk',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pystray\\_util\\gtk.py',
   'PYMODULE'),
  ('pystray._util',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pystray\\_util\\__init__.py',
   'PYMODULE'),
  ('pystray._info',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pystray\\_info.py',
   'PYMODULE'),
  ('pystray._base',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pystray\\_base.py',
   'PYMODULE'),
  ('pystray._xorg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pystray\\_xorg.py',
   'PYMODULE'),
  ('pystray._win32',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pystray\\_win32.py',
   'PYMODULE'),
  ('pystray._gtk',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pystray\\_gtk.py',
   'PYMODULE'),
  ('pystray._darwin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pystray\\_darwin.py',
   'PYMODULE'),
  ('pystray._appindicator',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pystray\\_appindicator.py',
   'PYMODULE'),
  ('pystray._dummy',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pystray\\_dummy.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.constants',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('pynput.mouse',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pynput\\mouse\\__init__.py',
   'PYMODULE'),
  ('pynput.keyboard',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pynput\\keyboard\\__init__.py',
   'PYMODULE'),
  ('pynput',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pynput\\__init__.py',
   'PYMODULE'),
  ('pynput.mouse._xorg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pynput\\mouse\\_xorg.py',
   'PYMODULE'),
  ('pynput.mouse._dummy',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pynput\\mouse\\_dummy.py',
   'PYMODULE'),
  ('pynput.mouse._darwin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pynput\\mouse\\_darwin.py',
   'PYMODULE'),
  ('pynput.keyboard._xorg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pynput\\keyboard\\_xorg.py',
   'PYMODULE'),
  ('pynput.keyboard._uinput',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pynput\\keyboard\\_uinput.py',
   'PYMODULE'),
  ('pynput.keyboard._dummy',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pynput\\keyboard\\_dummy.py',
   'PYMODULE'),
  ('pynput.keyboard._darwin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pynput\\keyboard\\_darwin.py',
   'PYMODULE'),
  ('pynput._util.xorg_keysyms',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pynput\\_util\\xorg_keysyms.py',
   'PYMODULE'),
  ('pynput._util.xorg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pynput\\_util\\xorg.py',
   'PYMODULE'),
  ('pynput._util.uinput',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pynput\\_util\\uinput.py',
   'PYMODULE'),
  ('pynput._util.darwin_vks',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pynput\\_util\\darwin_vks.py',
   'PYMODULE'),
  ('pynput._util.darwin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pynput\\_util\\darwin.py',
   'PYMODULE'),
  ('pynput._info',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pynput\\_info.py',
   'PYMODULE'),
  ('pyperclip',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\pyperclip\\__init__.py',
   'PYMODULE'),
  ('json',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\json\\scanner.py',
   'PYMODULE')])

('E:\\onedrive\\OneDrive\\Tools\\AI生成工具\\鼠标坐标捕捉工具\\dist\\鼠标坐标捕捉工具.exe',
 False,
 False,
 False,
 ['E:\\onedrive\\OneDrive\\Tools\\AI生成工具\\鼠标坐标捕捉工具\\rabbear16x16.ico'],
 None,
 False,
 False,
 '<?xml version="1.0" encoding="UTF-8" standalone="yes"?><assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0"><assemblyIdentity type="win32" name="mouse_coordinate_capture" processorArchitecture="amd64" version="1.0.0.0"/><trustInfo xmlns="urn:schemas-microsoft-com:asm.v3"><security><requestedPrivileges><requestedExecutionLevel level="asInvoker" uiAccess="false"/></requestedPrivileges></security></trustInfo><dependency><dependentAssembly><assemblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" language="*" processorArchitecture="*" version="6.0.0.0" publicKeyToken="6595b64144ccf1df"/></dependentAssembly></dependency><compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1"><application><supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f0}"/><supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/><supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/><supportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/><supportedOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/></application></compatibility><application xmlns="urn:schemas-microsoft-com:asm.v3"><windowsSettings><longPathAware xmlns="http://schemas.microsoft.com/SMI/2016/WindowsSettings">true</longPathAware></windowsSettings></application></assembly>',
 True,
 True,
 False,
 None,
 None,
 None,
 'E:\\onedrive\\OneDrive\\Tools\\AI生成工具\\鼠标坐标捕捉工具\\build\\mouse_coordinate_capture\\鼠标坐标捕捉工具.pkg',
 [('PYZ-00.pyz',
   'E:\\onedrive\\OneDrive\\Tools\\AI生成工具\\鼠标坐标捕捉工具\\build\\mouse_coordinate_capture\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'E:\\onedrive\\OneDrive\\Tools\\AI生成工具\\鼠标坐标捕捉工具\\build\\mouse_coordinate_capture\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'E:\\onedrive\\OneDrive\\Tools\\AI生成工具\\鼠标坐标捕捉工具\\build\\mouse_coordinate_capture\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'E:\\onedrive\\OneDrive\\Tools\\AI生成工具\\鼠标坐标捕捉工具\\build\\mouse_coordinate_capture\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'E:\\onedrive\\OneDrive\\Tools\\AI生成工具\\鼠标坐标捕捉工具\\build\\mouse_coordinate_capture\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'E:\\onedrive\\OneDrive\\Tools\\AI生成工具\\鼠标坐标捕捉工具\\build\\mouse_coordinate_capture\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_subprocess',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_subprocess.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('mouse_coordinate_capture',
   'E:\\onedrive\\OneDrive\\Tools\\AI生成工具\\鼠标坐标捕捉工具\\mouse_coordinate_capture.py',
   'PYSOURCE'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\VCRUNTIME140.dll',
   'BINARY'),
  ('python38.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\python38.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('libopenblas64__v0.3.21-gcc_10_3_0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\.libs\\libopenblas64__v0.3.21-gcc_10_3_0.dll',
   'BINARY'),
  ('_lzma',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('select',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_hashlib',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_ctypes',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('win32wnet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\win32\\win32wnet.pyd',
   'EXTENSION'),
  ('_ssl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_queue',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_multiprocessing',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_decimal',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('unicodedata',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('win32api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('PIL._webp',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\_webp.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PIL._imagingtk',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\_imagingtk.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PIL._imagingcms',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\_imagingcms.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.core._multiarray_tests',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\core\\_multiarray_tests.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('psutil._psutil_windows',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\psutil\\_psutil_windows.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.core._multiarray_umath',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\core\\_multiarray_umath.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('win32pdh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy.linalg.lapack_lite',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\linalg\\lapack_lite.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('numpy.random.mtrand',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\random\\mtrand.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._sfc64',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\random\\_sfc64.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._philox',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\random\\_philox.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._pcg64',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\random\\_pcg64.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._mt19937',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\random\\_mt19937.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random.bit_generator',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\random\\bit_generator.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._generator',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\random\\_generator.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._bounded_integers',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.random._common',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\random\\_common.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.fft._pocketfft_internal',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('numpy.linalg._umath_linalg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PIL._imagingmath',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\_imagingmath.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\_cffi_backend.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('PIL._imaging',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PIL\\_imaging.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('_tkinter',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\DLLs\\_tkinter.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-1_1-x64.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\Library\\bin\\libcrypto-1_1-x64.dll',
   'BINARY'),
  ('libffi-7.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\DLLs\\libffi-7.dll',
   'BINARY'),
  ('pywintypes38.dll',
   'C:\\ProgramData\\Anaconda3\\Library\\bin\\pywintypes38.dll',
   'BINARY'),
  ('libssl-1_1-x64.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\Library\\bin\\libssl-1_1-x64.dll',
   'BINARY'),
  ('tk86t.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\DLLs\\tk86t.dll',
   'BINARY'),
  ('tcl86t.dll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\DLLs\\tcl86t.dll',
   'BINARY'),
  ('base_library.zip',
   'E:\\onedrive\\OneDrive\\Tools\\AI生成工具\\鼠标坐标捕捉工具\\build\\mouse_coordinate_capture\\base_library.zip',
   'DATA'),
  ('tcl\\tzdata\\America\\Asuncion',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tallinn',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('tcl\\tzdata\\Australia\\ACT',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qyzylorda',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('tcl\\tzdata\\America\\Cordoba',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('tk\\tclIndex',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\tclIndex',
   'DATA'),
  ('tcl\\tzdata\\America\\Manaus',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('tcl\\tzdata\\America\\Lima',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('tk\\ttk\\notebook.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayenne',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Johns',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ceuta',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('tk\\ttk\\progress.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Port-au-Prince',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaSur',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuala_Lumpur',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Buenos_Aires',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Reykjavik',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('tk\\images\\pwrdLogo.eps',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Prague',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dubai',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Tell_City',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtobe',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('tcl\\msgs\\es_pa.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('tk\\pkgIndex.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Rarotonga',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('tcl\\tzdata\\America\\Guayaquil',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Brussels',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Seoul',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dakar',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('tcl\\encoding\\ksc5601.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('tcl\\msgs\\de_be.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Grenada',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('tk\\text.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\text.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Miquelon',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('tk\\panedwindow.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('tcl\\tzdata\\Greenwich',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('tk\\ttk\\vistaTheme.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('tk\\images\\pwrdLogo100.gif',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('tcl\\tzdata\\America\\Mendoza',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Reunion',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('tcl\\tzdata\\America\\Ojinaga',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('tcl\\msgs\\es_cr.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('tcl\\msgs\\ro.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Famagusta',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('tcl\\msgs\\de_at.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ujung_Pandang',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('tk\\scale.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\scale.tcl',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9YDT',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Warsaw',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('tcl\\msgs\\eu.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('tk\\tk.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\tk.tcl',
   'DATA'),
  ('tk\\msgbox.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('tcl\\tzdata\\Cuba',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimbu',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('tcl\\msgs\\es_hn.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lome',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Muscat',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Lisbon',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('tcl\\tzdata\\America\\Matamoros',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+3',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('tcl\\tzdata\\America\\Indianapolis',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Comoro',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Vostok',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('tcl\\tzdata\\America\\Nome',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7MDT',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('tcl\\msgs\\sh.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Niue',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('tcl\\tzdata\\US\\Alaska',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Honolulu',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('tcl\\http1.0\\pkgIndex.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Eucla',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('tcl\\encoding\\cp1257.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('tk\\msgs\\fr.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('tk\\msgs\\it.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('tk\\msgs\\ru.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('tcl\\tzdata\\US\\Michigan',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Majuro',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Samarkand',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Mawson',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('tcl\\tzdata\\America\\Rankin_Inlet',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('tcl\\tzdata\\America\\Monterrey',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tbilisi',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dili',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Ulyanovsk',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('tcl\\encoding\\cp869.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Juan',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('tcl\\encoding\\macIceland.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('tcl\\tzdata\\UCT',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Djibouti',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('tcl\\auto.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\auto.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Araguaina',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('tcl\\encoding\\macCroatian.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Barbados',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('tcl\\msgs\\ru_ua.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Bermuda',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Brunei',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('tcl\\msgs\\te_in.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('tcl\\msgs\\pt.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Funafuti',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('tcl\\tzdata\\EST5EDT',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('tcl\\tzdata\\W-SU',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Chuuk',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('tcl\\encoding\\cp1256.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Buenos_Aires',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('tcl\\tzdata\\America\\Creston',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('tk\\iconlist.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Edmonton',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kigali',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('tcl\\tzdata\\America\\New_York',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('tcl\\tzdata\\America\\Knox_IN',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('tcl\\tzdata\\America\\Port_of_Spain',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Dar_es_Salaam',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('tcl\\msgs\\mr.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Belgrade',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Damascus',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('tcl\\tzdata\\GB-Eire',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('tcl\\tzdata\\America\\Iqaluit',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('tcl\\tzdata\\America\\Sitka',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Apia',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('tcl\\tzdata\\America\\Denver',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Freetown',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('tk\\images\\README',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Maldives',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Almaty',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('tcl\\tzdata\\America\\Thule',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('tcl\\word.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\word.tcl',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Kerguelen',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('tcl\\tzdata\\America\\Menominee',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vatican',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Skopje',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('tcl\\tzdata\\America\\Santa_Isabel',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Chatham',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('tk\\ttk\\xpTheme.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('tcl\\encoding\\iso8859-13.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('tcl\\msgs\\bg.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('tcl\\msgs\\fi.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('tcl\\msgs\\mt.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-5',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Rangoon',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Auckland',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('tcl\\tzdata\\America\\Cancun',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('tcl\\msgs\\zh_hk.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Blantyre',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('tcl\\msgs\\ja.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('tcl\\encoding\\koi8-u.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yekaterinburg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('tcl\\msgs\\fr_ch.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Volgograd',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('tk\\images\\tai-ku.gif',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-7',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('tcl\\msgs\\ga.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Amman',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Samoa',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('tcl\\tzdata\\America\\Punta_Arenas',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Zulu',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('tcl\\tzdata\\America\\Aruba',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('tcl\\msgs\\en_za.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('tcl\\encoding\\cp1258.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('tcl\\tzdata\\US\\Eastern',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Perth',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Malabo',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('tcl\\msgs\\nl.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Petersburg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('tcl\\encoding\\euc-cn.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Galapagos',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('tcl\\tzdata\\America\\Puerto_Rico',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('tcl\\tzdata\\America\\Santarem',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Conakry',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('tcl\\msgs\\ta.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Noronha',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('tcl\\encoding\\iso8859-6.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pitcairn',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('tcl\\msgs\\en_ie.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('tcl\\encoding\\cp855.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Macquarie',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Madeira',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('tcl\\msgs\\it.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('tk\\license.terms',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\McMurdo',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-6',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('tcl\\msgs\\bn.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('tcl\\msgs\\en_hk.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nouakchott',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Brazzaville',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('tcl\\tzdata\\America\\Swift_Current',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+7',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\St_Helena',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('tcl\\http1.0\\http.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('tcl\\encoding\\gb2312-raw.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('tcl\\msgs\\gv.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Tortola',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('tcl\\msgs\\ar_sy.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Ponape',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('tcl\\msgs\\sl.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('tcl\\msgs\\eu_es.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bamako',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Ushuaia',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vincennes',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Mendoza',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('tcl\\tzdata\\America\\Guyana',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('tcl\\encoding\\macUkraine.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Cambridge_Bay',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Monrovia',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('tcl\\msgs\\en_au.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bujumbura',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baghdad',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('tcl\\msgs\\de.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Minsk',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('tcl\\msgs\\es_mx.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('tcl\\tzdata\\US\\Indiana-Starke',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('tcl\\msgs\\be.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('tcl\\msgs\\pl.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Porto-Novo',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('tcl\\encoding\\dingbats.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Antananarivo',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('tk\\ttk\\ttk.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('tcl\\encoding\\cp1251.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Tucuman',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('tcl\\msgs\\es_bo.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('tcl\\msgs\\bn_in.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Wayne',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pago_Pago',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('tcl\\tzdata\\US\\Samoa',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('tcl\\tzdata\\America\\Godthab',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('tcl\\tzdata\\America\\Rosario',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('tcl\\msgs\\gl_es.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Chisinau',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Gibraltar',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('tcl\\tzdata\\America\\Virgin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Easter',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('tcl\\tzdata\\Navajo',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('tcl\\msgs\\fo.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('tcl\\msgs\\lv.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kabul',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('tcl\\msgs\\es_ar.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.0.tm',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\..\\tcl8\\8.6\\http-2.9.0.tm',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Vladivostok',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('tcl\\encoding\\gb12345.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('tcl\\msgs\\vi.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Novokuznetsk',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Montserrat',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Harare',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Addis_Ababa',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('tcl\\msgs\\kok.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\East',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('tcl\\tzdata\\America\\Montreal',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Lord_Howe',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('tcl\\encoding\\cp775.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Saigon',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Saipan',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Troll',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('tcl\\encoding\\cp874.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('tcl\\tzdata\\US\\Aleutian',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('tk\\ttk\\combobox.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('tcl\\tzdata\\Kwajalein',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('tcl\\encoding\\macCyrillic.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Mariehamn',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Moscow',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('tcl\\tzdata\\America\\Santo_Domingo',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('tcl\\tzdata\\America\\Grand_Turk',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Khartoum',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Windhoek',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('tk\\choosedir.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Vancouver',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Accra',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('tcl\\encoding\\cp1252.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Asmera',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\..\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('tcl\\msgs\\fr.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('tk\\console.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Indianapolis',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('tk\\bgerror.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Thimphu',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Vevay',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('tk\\optMenu.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Singapore',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kiritimati',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('tcl\\msgs\\lt.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Pacific',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('tk\\menu.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\menu.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ulaanbaatar',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('tcl\\msgs\\es_ni.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Athens',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('tcl\\tzdata\\America\\Yakutat',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('tcl\\clock.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+6',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\South_Georgia',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('tcl\\tzdata\\America\\Mexico_City',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.0.tm',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\..\\tcl8\\8.5\\tcltest-2.5.0.tm',
   'DATA'),
  ('tk\\images\\logo.eps',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('tcl\\tzdata\\PST8PDT',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('tcl\\tzdata\\America\\Inuvik',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Taipei',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('tcl\\msgs\\pt_br.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-2',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('tcl\\opt0.4\\pkgIndex.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Rome',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Universal',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('tk\\button.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('tcl\\msgs\\es_py.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('tcl\\msgs\\gv_gb.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('tcl\\msgs\\uk.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tel_Aviv',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tahiti',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('tcl\\tzdata\\HST',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6CDT',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('tk\\ttk\\button.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Lower_Princes',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('tcl\\msgs\\ar.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('tcl\\msgs\\ko.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('tcl\\msgs\\af_za.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Pohnpei',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('tk\\obsolete.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('tk\\images\\pwrdLogo175.gif',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('tcl\\tzdata\\ROK',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Sofia',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('tcl\\encoding\\cp737.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('tcl\\encoding\\koi8-r.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5EDT',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('tcl\\tzdata\\America\\Merida',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('tcl\\msgs\\th.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aden',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('tk\\msgs\\da.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Norfolk',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('tk\\msgs\\el.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bangui',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('tcl\\encoding\\iso8859-7.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('tcl\\tzdata\\WET',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('tcl\\encoding\\cp1250.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\ComodRivadavia',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lubumbashi',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('tcl\\tzdata\\America\\Recife',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('tcl\\tzdata\\America\\Fort_Nelson',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Winamac',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Lindeman',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('tcl\\encoding\\iso8859-1.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ho_Chi_Minh',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('tcl\\msgs\\fo_fo.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Riga',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('tcl\\tzdata\\America\\Sao_Paulo',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Aqtau',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Vientiane',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Cordoba',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('tcl\\tzdata\\Europe\\London',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+9',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yangon',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('tcl\\tzdata\\America\\Guatemala',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('tcl\\init.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\init.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Metlakatla',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('tcl\\tzdata\\Japan',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('tcl\\msgs\\es.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Rainy_River',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('tcl\\msgs\\et.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Sydney',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Atlantic',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('tcl\\tzdata\\America\\Boise',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('tcl\\encoding\\cp865.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('tcl\\encoding\\ascii.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('tcl\\msgs\\hr.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Davis',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Khandyga',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('tcl\\msgs\\zh.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('tcl\\msgs\\hi.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Cocos',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('tcl\\tzdata\\America\\Danmarkshavn',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Manila',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('tcl\\tzdata\\Europe\\San_Marino',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Uzhgorod',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macau',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Chagos',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Timbuktu',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Tripoli',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('tcl\\msgs\\he.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('tcl\\tzdata\\US\\Mountain',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('tk\\tkfbox.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Istanbul',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('tcl\\tzdata\\America\\Paramaribo',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Salta',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Guam',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\MST7',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tokyo',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('tcl\\tzdata\\America\\Boa_Vista',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('tcl\\tzdata\\America\\Campo_Grande',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('tcl\\encoding\\iso8859-2.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('tcl\\tzdata\\Singapore',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('tcl\\tzdata\\America\\Guadeloupe',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('tk\\listbox.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('tk\\msgs\\hu.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Baku',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('tk\\msgs\\eo.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Andorra',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('tcl\\encoding\\macJapan.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('tcl\\tzdata\\Iceland',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuching',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('tcl\\encoding\\gb2312.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+12',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('tcl\\encoding\\macGreek.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('tcl\\tzdata\\US\\Pacific',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('tk\\msgs\\cs.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Adelaide',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Banjul',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('tcl\\msgs\\es_pe.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('tcl\\encoding\\jis0201.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-3.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Maceio',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('tcl\\encoding\\cp1253.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Goose_Bay',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('tcl\\msgs\\es_co.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wallis',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('tcl\\msgs\\sk.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Helsinki',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-0',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Azores',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Gaza',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('tcl\\encoding\\cp861.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('tk\\ttk\\scale.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kosrae',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('tk\\images\\pwrdLogo200.gif',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('tcl\\tzdata\\America\\Havana',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('tk\\entry.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\entry.tcl',
   'DATA'),
  ('tcl\\tzdata\\Hongkong',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yakutsk',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Mazatlan',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('tcl\\msgs\\ar_lb.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('tk\\ttk\\utils.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Krasnoyarsk',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Los_Angeles',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('tcl\\tzdata\\Zulu',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Yap',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Qatar',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('tcl\\encoding\\cp1254.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\BajaNorte',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT0',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Riyadh',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Simferopol',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('tk\\ttk\\scrollbar.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('tcl\\tzdata\\GB',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('tcl\\msgs\\zh_tw.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('tcl\\msgs\\es_ec.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Omsk',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Gaborone',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+4',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('tcl\\tzdata\\America\\Phoenix',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Bissau',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('tk\\ttk\\treeview.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('tk\\mkpsenc.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kathmandu',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tomsk',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Shiprock',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Niamey',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('tcl\\tzdata\\Canada\\East-Saskatchewan',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('tcl\\history.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ashgabat',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Atyrau',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Beirut',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vienna',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dacca',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('tcl\\tzdata\\ROC',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-4',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Yancowinna',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Urumqi',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Jan_Mayen',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Casey',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('tcl\\tzdata\\America\\Winnipeg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('tcl\\msgs\\zh_sg.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bratislava',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('tcl\\msgs\\is.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('tcl\\msgs\\ta_in.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kirov',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('tcl\\tzdata\\America\\Glace_Bay',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('tcl\\tzdata\\Australia\\West',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Port_Moresby',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lusaka',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('tcl\\tzdata\\Africa\\El_Aaiun',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\EST5',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Magadan',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Malta',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('tcl\\tzdata\\America\\Louisville',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('tk\\ttk\\cursors.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hebron',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('tcl\\tzdata\\America\\Eirunepe',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('tcl\\tzdata\\America\\Tijuana',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Lagos',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Saskatchewan',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-3',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('tcl\\tzdata\\America\\Adak',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Samara',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('tcl\\tzdata\\America\\Bahia',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Guernsey',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('tcl\\tzdata\\Australia\\NSW',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bahrain',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('tcl\\msgs\\ms.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Cuiaba',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('tcl\\tzdata\\NZ',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Copenhagen',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chungking',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Saratov',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('tcl\\encoding\\cp936.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Astrakhan',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('tcl\\encoding\\iso8859-5.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maseru',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('tcl\\tzdata\\America\\Fortaleza',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('tcl\\msgs\\fa_ir.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Jujuy',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Queensland',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('tcl\\tzdata\\America\\Detroit',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chongqing',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Tasmania',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('tcl\\msgs\\fr_ca.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('tcl\\msgs\\hu.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\DumontDUrville',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('tcl\\encoding\\cp949.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kuwait',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+11',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('tcl\\tzdata\\Jamaica',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('tcl\\msgs\\es_uy.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('tcl\\encoding\\ebcdic.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('tcl\\tzdata\\Egypt',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('tcl\\encoding\\cp950.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Calcutta',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('tk\\msgs\\pl.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('tcl\\tzdata\\GMT-0',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('tcl\\tzdata\\MET',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('tcl\\encoding\\cp1255.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('tcl\\msgs\\fa.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-9',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('tcl\\encoding\\tis-620.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+0',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Douala',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ust-Nera',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zurich',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Anadyr',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Oslo',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('tcl\\encoding\\cp932.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('tcl\\encoding\\euc-kr.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Lucia',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('tcl\\msgs\\en_sg.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('tcl\\msgs\\en_be.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Maputo',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('tcl\\tzdata\\America\\La_Paz',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('tcl\\tzdata\\US\\Arizona',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('tcl\\msgs\\en_ph.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kashgar',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Bucharest',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Victoria',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Choibalsan',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Kitts',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('tcl\\encoding\\iso2022.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Canberra',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('tk\\ttk\\entry.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Asmara',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Mountain',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Knox',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('tcl\\msgs\\da.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('tcl\\encoding\\macCentEuro.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('tk\\megawidget.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-1',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Pyongyang',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('tcl\\encoding\\macRoman.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Caracas',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.14.tm',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\..\\tcl8\\8.4\\platform-1.0.14.tm',
   'DATA'),
  ('tcl\\encoding\\iso8859-16.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('tcl\\msgs\\ms_my.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('tk\\msgs\\nl.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bangkok',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('tcl\\msgs\\es_gt.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('tk\\ttk\\menubutton.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('tcl\\encoding\\jis0208.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Palau',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Chita',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\La_Rioja',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('tk\\unsupported.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('tcl\\tzdata\\Australia\\South',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-8',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kampala',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8PDT',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('tcl\\tzdata\\Mexico\\General',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('tcl\\tzdata\\America\\Martinique',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kolkata',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('tk\\xmfbox.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('tcl\\encoding\\cp850.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('tcl\\tzdata\\EST',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('tk\\images\\logo64.gif',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vaduz',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('tcl\\msgs\\en_gb.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tirane',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('tcl\\tzdata\\EET',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('tk\\icons.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\icons.tcl',
   'DATA'),
  ('tcl\\tzdata\\Iran',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('tcl\\tzdata\\US\\Central',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Nairobi',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('tcl\\tzdata\\America\\Marigot',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\New_Salem',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('tcl\\msgs\\mk.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Belem',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Sarajevo',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Stockholm',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Jujuy',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Kinshasa',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Hobart',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('tcl\\msgs\\eo.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('tcl\\encoding\\cp857.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('tk\\images\\logoMed.gif',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\San_Luis',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Nicosia',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('tcl\\tzdata\\America\\Indiana\\Marengo',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('tcl\\tzdata\\America\\Scoresbysund',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('tcl\\encoding\\cp437.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Vilnius',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zaporozhye',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('tcl\\tzdata\\America\\Belize',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Johnston',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Marquesas',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Barthelemy',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('tk\\ttk\\sizegrip.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+5',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mbabane',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('tcl\\tzdata\\America\\Dominica',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('tcl\\msgs\\ar_jo.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Nassau',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('tcl\\encoding\\big5.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-10.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('tcl\\encoding\\iso8859-8.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Paris',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('tcl\\tzdata\\America\\Atikokan',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\HST10',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Zagreb',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\Acre',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('tcl\\encoding\\cp852.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('tk\\comdlg.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('tcl\\msgs\\el.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Managua',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('tcl\\msgs\\sv.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Bougainville',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Sao_Tome',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('tcl\\tzdata\\Australia\\LHI',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-11',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('tk\\ttk\\classicTheme.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('tcl\\msgs\\te.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Makassar',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('tcl\\msgs\\es_ve.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('tcl\\msgs\\id.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Rothera',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('tk\\msgs\\de.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Berlin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Karachi',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('tcl\\msgs\\en_in.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('tk\\msgs\\pt.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Kralendijk',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('tk\\ttk\\fonts.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faeroe',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Phnom_Penh',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('tcl\\tzdata\\US\\Hawaii',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('tcl\\tzdata\\America\\Santiago',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('tcl\\tzdata\\America\\Resolute',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Macao',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('tcl\\msgs\\gl.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('tcl\\package.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('tcl\\encoding\\iso8859-4.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('tcl\\tzdata\\Turkey',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('tcl\\msgs\\ko_kr.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('tcl\\tzdata\\MST7MDT',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+1',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Casablanca',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('tcl\\safe.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\safe.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Nicosia',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-10',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hovd',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+8',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Nauru',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-14',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Enderbury',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('tcl\\tzdata\\America\\Tegucigalpa',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Tunis',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jerusalem',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Faroe',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Luanda',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('tcl\\msgs\\kw_gb.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Syowa',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Kamchatka',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('tcl\\encoding\\macThai.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('tcl\\msgs\\es_cl.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('tcl\\encoding\\cp860.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Whitehorse',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('tk\\images\\pwrdLogo75.gif',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('tk\\ttk\\panedwindow.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Bahia_Banderas',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('tcl\\tzdata\\US\\East-Indiana',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('tcl\\msgs\\ca.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Efate',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('tk\\clrpick.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\West',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('tcl\\msgs\\kl_gl.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Jamaica',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('tk\\msgs\\sv.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Vincent',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('tcl\\tzdata\\America\\Coral_Harbour',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('tcl\\tzdata\\America\\Atka',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kiev',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('tcl\\tzdata\\America\\Blanc-Sablon',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Christmas',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Tiraspol',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('tk\\ttk\\defaults.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('tcl\\tzdata\\GMT0',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('tcl\\tzdata\\Israel',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('tcl\\msgs\\tr.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('tcl\\encoding\\macRomania.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('tk\\scrlbar.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Budapest',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('tcl\\tzdata\\Chile\\Continental',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('tcl\\msgs\\kok_in.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('tk\\tearoff.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('tcl\\tzdata\\NZ-CHAT',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('tcl\\tzdata\\America\\Dawson',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('tcl\\tzdata\\America\\Panama',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('tcl\\msgs\\af.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('tk\\fontchooser.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dhaka',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tashkent',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('tcl\\msgs\\nn.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('tcl\\tzdata\\PRC',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('tcl\\tzdata\\CST6CDT',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Hong_Kong',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ulan_Bator',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mahe',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('tcl\\encoding\\macDingbats.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('tcl\\encoding\\gb1988.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Cairo',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fakaofo',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('tk\\images\\logo100.gif',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Eastern',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('tcl\\encoding\\iso8859-14.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\..\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('tcl\\msgs\\es_sv.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('tcl\\tzdata\\Brazil\\DeNoronha',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('tcl\\msgs\\ga_ie.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('tcl\\tzdata\\Portugal',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-13',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Ashkhabad',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('tcl\\msgs\\ru.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mauritius',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('tcl\\tzdata\\Libya',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Cape_Verde',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('tcl\\tzdata\\GMT',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('tk\\ttk\\winTheme.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jakarta',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('tcl\\tzdata\\America\\Argentina\\Catamarca',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Mogadishu',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Louisville',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Juba',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Stanley',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Kaliningrad',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('tcl\\encoding\\symbol.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('tcl\\msgs\\kw.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('tcl\\encoding\\shiftjis.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('tk\\images\\logoLarge.gif',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('tk\\msgs\\es.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('tcl\\encoding\\cp866.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Madrid',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('tcl\\tzdata\\America\\Thunder_Bay',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Monaco',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('tcl\\encoding\\iso8859-15.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('tcl\\tzdata\\America\\Yellowknife',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('tcl\\encoding\\iso8859-9.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('tcl\\tzdata\\GMT+0',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('tcl\\msgs\\cs.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('tcl\\msgs\\sw.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Guadalcanal',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Irkutsk',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Noumea',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('tcl\\parray.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Antigua',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT-12',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Ljubljana',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('tcl\\tzdata\\America\\Chicago',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('tcl\\tzdata\\Etc\\UCT',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('tcl\\tzdata\\America\\Toronto',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('tcl\\tzdata\\Etc\\Greenwich',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('tcl\\encoding\\jis0212.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('tk\\images\\pwrdLogo150.gif',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('tcl\\tclIndex',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tclIndex',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Tehran',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\YST9',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('tcl\\msgs\\en_bw.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Currie',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('tcl\\msgs\\fa_in.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ndjamena',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('tcl\\msgs\\ar_in.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('tcl\\msgs\\hi_in.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tarawa',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('tk\\safetk.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+2',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Yukon',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('tcl\\tzdata\\America\\Dawson_Creek',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('tcl\\tzdata\\America\\Juneau',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('tcl\\msgs\\kl.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('tk\\focus.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\focus.tcl',
   'DATA'),
  ('tk\\ttk\\spinbox.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('tcl\\tzdata\\Arctic\\Longyearbyen',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Midway',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Abidjan',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('tcl\\tzdata\\UTC',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('tcl\\msgs\\nb.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Yerevan',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('tcl\\tzdata\\America\\St_Thomas',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('tcl\\tzdata\\Australia\\North',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\South_Pole',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('tcl\\msgs\\fr_be.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Harbin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Tongatapu',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Novosibirsk',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('tcl\\tzdata\\America\\Hermosillo',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('tcl\\tzdata\\US\\Pacific-New',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('tcl\\opt0.4\\optparse.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Ensenada',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('tcl\\tzdata\\America\\Anchorage',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Barnaul',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('tcl\\tzdata\\America\\Cayman',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('tcl\\tzdata\\America\\Bogota',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('tk\\ttk\\aquaTheme.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('tcl\\encoding\\cp862.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Wake',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('tk\\spinbox.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Costa_Rica',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Bishkek',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('tcl\\msgs\\id_id.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('tcl\\msgs\\es_pr.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('tcl\\msgs\\it_ch.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Podgorica',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('tcl\\msgs\\en_zw.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('tcl\\tzdata\\MST',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('tcl\\tzdata\\America\\Kentucky\\Monticello',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('tcl\\tzdata\\America\\Montevideo',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('tcl\\tzdata\\America\\Chihuahua',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Amsterdam',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('tcl\\tzdata\\Etc\\GMT+10',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Truk',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Brisbane',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Busingen',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('tcl\\msgs\\es_do.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('tcl\\tzdata\\Indian\\Mayotte',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('tk\\ttk\\clamTheme.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\America\\Curacao',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('tcl\\tzdata\\America\\Catamarca',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('tcl\\msgs\\nl_be.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Rio_Branco',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('tcl\\tzdata\\America\\Pangnirtung',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Jersey',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('tk\\dialog.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('tcl\\encoding\\iso2022-kr.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('tcl\\tzdata\\Antarctica\\Palmer',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('tcl\\tzdata\\Etc\\UTC',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Johannesburg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Libreville',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Beulah',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('tk\\ttk\\altTheme.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Gambier',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('tcl\\tzdata\\CET',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\CST6',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Broken_Hill',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('tcl\\tm.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tm.tcl',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Luxembourg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Dushanbe',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('tk\\palette.tcl',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('tcl\\msgs\\en_nz.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Belfast',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\AST4ADT',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('tcl\\encoding\\macTurkish.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('tcl\\tzdata\\Poland',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Srednekolymsk',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Kwajalein',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('tk\\msgs\\en.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Moncton',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Ouagadougou',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('tcl\\encoding\\cp863.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Shanghai',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Dublin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('tcl\\tzdata\\America\\North_Dakota\\Center',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Katmandu',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('tcl\\tzdata\\America\\Nipigon',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('tcl\\encoding\\iso2022-jp.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Pontianak',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('tcl\\tzdata\\America\\Regina',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Central',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('tcl\\tzdata\\Atlantic\\Canary',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('tcl\\tzdata\\Eire',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('tcl\\tzdata\\SystemV\\PST8',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Acre',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('tcl\\tzdata\\Africa\\Algiers',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('tcl\\msgs\\mr_in.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Halifax',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Jayapura',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Sakhalin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Colombo',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Istanbul',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('tcl\\tzdata\\America\\El_Salvador',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('tcl\\msgs\\sq.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('tcl\\tzdata\\America\\Anguilla',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('tcl\\encoding\\euc-jp.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('tcl\\tzdata\\Canada\\Newfoundland',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('tcl\\msgs\\en_ca.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('tcl\\msgs\\zh_cn.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('tcl\\tzdata\\Pacific\\Fiji',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Melbourne',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('tcl\\tzdata\\Europe\\Isle_of_Man',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('tcl\\tzdata\\Chile\\EasterIsland',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('tcl\\tzdata\\Australia\\Darwin',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('tcl\\tzdata\\Asia\\Oral',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('tcl\\msgs\\sr.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('tcl\\encoding\\cp864.enc',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('tk\\msgs\\en_gb.msg',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('tcl\\tzdata\\Universal',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('tcl\\tzdata\\America\\Porto_Velho',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('setuptools-54.0.0.dist-info\\dependency_links.txt',
   'c:\\programdata\\anaconda3\\envs\\qt38\\lib\\site-packages\\setuptools-54.0.0.dist-info\\dependency_links.txt',
   'DATA'),
  ('setuptools-54.0.0.dist-info\\RECORD',
   'c:\\programdata\\anaconda3\\envs\\qt38\\lib\\site-packages\\setuptools-54.0.0.dist-info\\RECORD',
   'DATA'),
  ('wheel-0.37.1-py3.9.egg-info\\PKG-INFO',
   'c:\\programdata\\anaconda3\\envs\\qt38\\lib\\site-packages\\wheel-0.37.1-py3.9.egg-info\\PKG-INFO',
   'DATA'),
  ('setuptools-54.0.0.dist-info\\LICENSE',
   'c:\\programdata\\anaconda3\\envs\\qt38\\lib\\site-packages\\setuptools-54.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools-54.0.0.dist-info\\METADATA',
   'c:\\programdata\\anaconda3\\envs\\qt38\\lib\\site-packages\\setuptools-54.0.0.dist-info\\METADATA',
   'DATA'),
  ('wheel-0.37.1-py3.9.egg-info\\dependency_links.txt',
   'c:\\programdata\\anaconda3\\envs\\qt38\\lib\\site-packages\\wheel-0.37.1-py3.9.egg-info\\dependency_links.txt',
   'DATA'),
  ('setuptools-54.0.0.dist-info\\WHEEL',
   'c:\\programdata\\anaconda3\\envs\\qt38\\lib\\site-packages\\setuptools-54.0.0.dist-info\\WHEEL',
   'DATA'),
  ('wheel-0.37.1-py3.9.egg-info\\top_level.txt',
   'c:\\programdata\\anaconda3\\envs\\qt38\\lib\\site-packages\\wheel-0.37.1-py3.9.egg-info\\top_level.txt',
   'DATA'),
  ('setuptools-54.0.0.dist-info\\REQUESTED',
   'c:\\programdata\\anaconda3\\envs\\qt38\\lib\\site-packages\\setuptools-54.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools-54.0.0.dist-info\\entry_points.txt',
   'c:\\programdata\\anaconda3\\envs\\qt38\\lib\\site-packages\\setuptools-54.0.0.dist-info\\entry_points.txt',
   'DATA'),
  ('wheel-0.37.1-py3.9.egg-info\\entry_points.txt',
   'c:\\programdata\\anaconda3\\envs\\qt38\\lib\\site-packages\\wheel-0.37.1-py3.9.egg-info\\entry_points.txt',
   'DATA'),
  ('wheel-0.37.1-py3.9.egg-info\\requires.txt',
   'c:\\programdata\\anaconda3\\envs\\qt38\\lib\\site-packages\\wheel-0.37.1-py3.9.egg-info\\requires.txt',
   'DATA'),
  ('wheel-0.37.1-py3.9.egg-info\\SOURCES.txt',
   'c:\\programdata\\anaconda3\\envs\\qt38\\lib\\site-packages\\wheel-0.37.1-py3.9.egg-info\\SOURCES.txt',
   'DATA'),
  ('setuptools-54.0.0.dist-info\\INSTALLER',
   'c:\\programdata\\anaconda3\\envs\\qt38\\lib\\site-packages\\setuptools-54.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools-54.0.0.dist-info\\top_level.txt',
   'c:\\programdata\\anaconda3\\envs\\qt38\\lib\\site-packages\\setuptools-54.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('wheel-0.37.1-py3.9.egg-info\\not-zip-safe',
   'c:\\programdata\\anaconda3\\envs\\qt38\\lib\\site-packages\\wheel-0.37.1-py3.9.egg-info\\not-zip-safe',
   'DATA'),
  ('rabbear16x16.ico',
   'E:\\onedrive\\OneDrive\\Tools\\AI生成工具\\鼠标坐标捕捉工具\\rabbear16x16.ico',
   'DATA')],
 [],
 False,
 True,
 1756295667,
 [('runw.exe',
   'C:\\ProgramData\\Anaconda3\\envs\\QT38\\lib\\site-packages\\PyInstaller\\bootloader\\Windows-64bit\\runw.exe',
   'EXECUTABLE')])

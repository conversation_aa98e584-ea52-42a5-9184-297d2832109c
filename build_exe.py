#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
鼠标坐标捕捉工具打包脚本
使用PyInstaller将Python程序打包成可执行文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print("✓ PyInstaller 已安装")
        return True
    except ImportError:
        print("✗ PyInstaller 未安装")
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    print("正在安装 PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✓ PyInstaller 安装成功")
        return True
    except subprocess.CalledProcessError:
        print("✗ PyInstaller 安装失败")
        return False

def create_spec_file():
    """创建PyInstaller规格文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['mouse_coordinate_capture.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('rabbear16x16.ico', '.'),
    ],
    hiddenimports=[
        'pynput.keyboard._win32',
        'pynput.mouse._win32',
        'PIL._tkinter_finder',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='鼠标坐标捕捉工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 设置为False以隐藏控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='rabbear16x16.ico',
)
'''
    
    with open('mouse_coordinate_capture.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    print("✓ 已创建 mouse_coordinate_capture.spec 文件")

def build_executable():
    """构建可执行文件"""
    print("开始构建可执行文件...")
    try:
        # 使用spec文件构建
        subprocess.check_call([
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "mouse_coordinate_capture.spec"
        ])
        print("✓ 构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 构建失败: {e}")
        return False

def organize_output():
    """整理输出文件"""
    dist_dir = Path("dist")
    if dist_dir.exists():
        exe_file = dist_dir / "鼠标坐标捕捉工具.exe"
        if exe_file.exists():
            # 创建发布目录
            release_dir = Path("release")
            if release_dir.exists():
                shutil.rmtree(release_dir)
            release_dir.mkdir()
            
            # 复制可执行文件
            shutil.copy2(exe_file, release_dir / "鼠标坐标捕捉工具.exe")
            
            # 复制图标文件（运行时需要）
            if Path("rabbear16x16.ico").exists():
                shutil.copy2("rabbear16x16.ico", release_dir / "rabbear16x16.ico")
            
            # 复制使用说明
            if Path("用法.txt").exists():
                shutil.copy2("用法.txt", release_dir / "用法.txt")
            
            print(f"✓ 发布文件已整理到 {release_dir} 目录")
            print(f"✓ 可执行文件大小: {exe_file.stat().st_size / 1024 / 1024:.1f} MB")
            return True
    
    print("✗ 未找到构建输出文件")
    return False

def cleanup():
    """清理临时文件"""
    cleanup_dirs = ["build", "__pycache__"]
    cleanup_files = ["mouse_coordinate_capture.spec"]
    
    for dir_name in cleanup_dirs:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✓ 已清理 {dir_name} 目录")
    
    for file_name in cleanup_files:
        if os.path.exists(file_name):
            os.remove(file_name)
            print(f"✓ 已清理 {file_name} 文件")

def main():
    """主函数"""
    print("=" * 50)
    print("鼠标坐标捕捉工具 - 打包脚本")
    print("=" * 50)
    
    # 检查必要文件
    required_files = ["mouse_coordinate_capture.py", "rabbear16x16.ico"]
    for file_name in required_files:
        if not os.path.exists(file_name):
            print(f"✗ 缺少必要文件: {file_name}")
            return False
    
    # 检查并安装PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            return False
    
    # 创建规格文件
    create_spec_file()
    
    # 构建可执行文件
    if not build_executable():
        return False
    
    # 整理输出文件
    if not organize_output():
        return False
    
    # 清理临时文件
    cleanup()
    
    print("\n" + "=" * 50)
    print("✓ 打包完成！")
    print("✓ 可执行文件位于 release 目录中")
    print("✓ 您可以将 release 目录中的文件分发给其他用户")
    print("=" * 50)
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n打包失败，请检查错误信息")
        sys.exit(1)

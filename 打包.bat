@echo off
chcp 65001 >nul
title 鼠标坐标捕捉工具 - 打包脚本

echo.
echo ================================================
echo 鼠标坐标捕捉工具 - 自动打包
echo ================================================
echo.

:: 设置Python环境路径
set PYTHON_PATH=C:/ProgramData/Anaconda3/envs/QT38/python.exe

:: 检查Python环境是否存在
if not exist "%PYTHON_PATH%" (
    echo 错误: 找不到Python环境: %PYTHON_PATH%
    echo 请检查Python环境路径是否正确
    pause
    exit /b 1
)

echo 使用Python环境: %PYTHON_PATH%
echo.

:: 运行打包脚本
echo 开始执行打包脚本...
echo.
"%PYTHON_PATH%" build_exe.py

:: 检查打包结果
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ================================================
    echo 打包成功完成！
    echo ================================================
    echo.
    echo 可执行文件已生成在 release 目录中
    echo 您可以将该目录中的文件分发给其他用户使用
    echo.
    
    :: 询问是否打开release目录
    set /p open_folder="是否打开release目录? (y/n): "
    if /i "%open_folder%"=="y" (
        if exist "release" (
            explorer "release"
        ) else (
            echo release目录不存在
        )
    )
) else (
    echo.
    echo ================================================
    echo 打包失败！
    echo ================================================
    echo.
    echo 请检查上面的错误信息并重试
)

echo.
pause
